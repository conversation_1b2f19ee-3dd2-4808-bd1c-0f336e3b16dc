version: "3.0"
services:
  amap-report-management-service:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: "amap-report-management-service"
    container_name: "amap-report-management-service"
    ports:
      - "4207:4207"
    restart: on-failure
    environment:
      - KAFKA_BOOTSTRAP_SERVERS=kafka:9092
      - REDIS_HOST=report-mgt-redis
      - REDIS_PORT=6381
    networks:
      - kafka-network
      - default
    depends_on:
      - report-mgt-redis
    env_file:
      - .env
    deploy:
      resources:
        limits:
          cpus: "0.25"
          memory: 512m

  report-mgt-redis:
    image: "bitnami/redis:latest"
    container_name: report-mgt-redis
    restart: unless-stopped
    tty: true
    volumes:
      - report-mgt-redis-data:/data
    ports:
      - "6381:6381"
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
      - REDIS_PORT_NUMBER=6381
    networks:
      - kafka-network
    deploy:
      resources:
        limits:
          cpus: "0.1"
          memory: 256m

volumes:
  report-mgt-redis-data:
    driver: local

networks:
  kafka-network:
    external:
      name: amap-kafka_kafka-network
