/**
 * Service implementation for handling candidate question results and flags.
 */
package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.FlaggedInputDTO;
import com.amap.amapreportmanagementservice.entity.*;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.mapper.QuestionMapper;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.CandidateQuestionRepository;
import com.amap.amapreportmanagementservice.repository.TestQuestionRepository;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "services_logger")
public class CandidateQuestionServiceImpl implements CandidateQuestionService {
    private final CandidateQuestionRepository candidateQuestionRepository;
    private final AssessmentTestRepository assessmentTestRepository;
    private final TestQuestionRepository testQuestionRepository;

    /**
     * Saves candidate question results based on the assessment progress data.
     *
     * @param assessmentProgressDTO The assessment progress data.
     * @throws ProcessFailedException If the process of saving candidate question results fails.
     */
    @Override
    public void saveCandidateQuestionResult(AssessmentProgressDTO assessmentProgressDTO) {

        try {
            assessmentProgressDTO.getAssessment().getTests().forEach(candidateTestDTO -> {
                String testId = candidateTestDTO.getId();
                String pk = KeyBuilder.candidateQuestionPK(assessmentProgressDTO.getOrganizationId(), assessmentProgressDTO.getAssessmentId(), testId);

                candidateTestDTO.getQuestions().forEach(questionDTO -> {

                    String questionId = questionDTO.getId();
                    String sk = KeyBuilder.candidateQuestionSK(questionId, assessmentProgressDTO.getEmail(), assessmentProgressDTO.getId());
                    CandidateQuestionResult candidateQuestion = new CandidateQuestionResult();
                    candidateQuestion.setPk(pk);
                    candidateQuestion.setSk(sk);
                    candidateQuestion.setCandidateId(assessmentProgressDTO.getId());
                    candidateQuestion.setCandidateEmail(assessmentProgressDTO.getEmail());
                    candidateQuestion.setOrganizationId(assessmentProgressDTO.getOrganizationId());
                    candidateQuestion.setTimeLimit(questionDTO.getTimeLimit());
                    candidateQuestion.setCreatedAt(String.valueOf(LocalDateTime.now()));
                    candidateQuestion.setDomain(questionDTO.getDomain());
                    candidateQuestion.setCategory(questionDTO.getCategory());

                    candidateQuestionRepository.saveCandidateQuestion(candidateQuestion);

                });
            });
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }

    }

    /**
     * Saves candidate question results with answers from completed assessment data.
     *
     * @param assessmentInputDTO The assessment input data.
     * @throws ProcessFailedException If the process of saving candidate question results fails.
     */
    @Override
    public void saveCandidateQuestionResultFromAssessmentInput(AssessmentInputDTO assessmentInputDTO) {
        try {
            assessmentInputDTO.getTestResults().forEach(testResultInputDTO -> {
                String testId = testResultInputDTO.getTestId();
                String pk = KeyBuilder.candidateQuestionPK(assessmentInputDTO.getOrganizationId(), assessmentInputDTO.getAssessmentId(), testId);

                testResultInputDTO.getQuestionResults().forEach(questionDTO -> {

                    String questionId = questionDTO.getQuestionId();
                    String sk = KeyBuilder.candidateQuestionSK(questionId, assessmentInputDTO.getEmail(), assessmentInputDTO.getTestTakerId());

                    CandidateQuestionResult candidateQuestion = new CandidateQuestionResult();
                    candidateQuestion.setPk(pk);
                    candidateQuestion.setSk(sk);
                    candidateQuestion.setCandidateId(assessmentInputDTO.getTestTakerId());
                    candidateQuestion.setCandidateEmail(assessmentInputDTO.getEmail());
                    candidateQuestion.setOrganizationId(assessmentInputDTO.getOrganizationId());
                    candidateQuestion.setCreatedAt(String.valueOf(LocalDateTime.now()));
                    candidateQuestion.setCandidateMarks(questionDTO.getScored());
                    candidateQuestion.setTotalScore(questionDTO.getScore());
                    candidateQuestion.setQuestionType(questionDTO.getQuestionType());
                    candidateQuestion.setQuestionId(questionDTO.getQuestionId());
                    candidateQuestion.setQuestionText(questionDTO.getQuestionText());
                    candidateQuestion.setDifficultyLevel(questionDTO.getDifficultyLevel());
                    candidateQuestion.setCodeReview(questionDTO.getCodeReview());

                    List<String> testTakerAnswers = questionDTO.getTestTakerAnswers();
                    if (testTakerAnswers != null) {
                        candidateQuestion.setTestTakerAnswers(testTakerAnswers);
                    }

                    QuestionMapper.mapToCandidateQuestionResult(questionDTO, candidateQuestion);

                    if (candidateQuestion.getCandidateMarks() == candidateQuestion.getTotalScore()
                            && candidateQuestion.getTotalScore() > 0) {
                        candidateQuestion.setIsAnswerCorrect("CORRECT");
                    }

                    if (testTakerAnswers != null && !testTakerAnswers.isEmpty()) {
                        candidateQuestion.setAnswered(true);
                    }

                    candidateQuestionRepository.saveCandidateQuestion(candidateQuestion);
                });
            });
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    /**
     * Updates candidate question results based on the assessment input data.
     *
     * @param assessmentInputDTO The assessment input data.
     * @throws ProcessFailedException If the process of updating candidate question results fails.
     */
    @Override
    public void updateCandidateQuestionResult(AssessmentInputDTO assessmentInputDTO) {
        try {
            assessmentInputDTO.getTestResults().forEach(testResultInputDTO -> {
                String testId = testResultInputDTO.getTestId();
                String pk = KeyBuilder.candidateQuestionPK(assessmentInputDTO.getOrganizationId(), assessmentInputDTO.getAssessmentId(), testId);

                testResultInputDTO.getQuestionResults().forEach(questionDTO -> {

                    String questionId = questionDTO.getQuestionId();
                    String sk = KeyBuilder.candidateQuestionSK(questionId, assessmentInputDTO.getEmail(), assessmentInputDTO.getTestTakerId());
                    Key candidateQuestionResultKey = Key.builder().partitionValue(pk).sortValue(sk).build();

                    List<CandidateQuestionResult> candidateQuestionResults = candidateQuestionRepository.getCandidateQuestions(candidateQuestionResultKey);

                    for (CandidateQuestionResult candidateQuestionResult : candidateQuestionResults) {
                        candidateQuestionResult.setCandidateMarks(questionDTO.getScored());
                        candidateQuestionResult.setTotalScore(questionDTO.getScore());
                        candidateQuestionResult.setUpdatedAt(String.valueOf(LocalDateTime.now()));
                        candidateQuestionResult.setQuestionType(questionDTO.getQuestionType());
                        candidateQuestionResult.setQuestionId(questionDTO.getQuestionId());
                        candidateQuestionResult.setQuestionText(questionDTO.getQuestionText());
                        candidateQuestionResult.setDifficultyLevel(questionDTO.getDifficultyLevel());
                        candidateQuestionResult.setCodeReview(questionDTO.getCodeReview());
                        List<String> testTakerAnswers = questionDTO.getTestTakerAnswers();
                        if (testTakerAnswers != null) {
                            candidateQuestionResult.setTestTakerAnswers(testTakerAnswers);
                        }
                        QuestionMapper.mapToCandidateQuestionResult(questionDTO, candidateQuestionResult);
                        if (candidateQuestionResult.getCandidateMarks() == candidateQuestionResult.getTotalScore()
                                && candidateQuestionResult.getTotalScore() > 0) {
                            candidateQuestionResult.setIsAnswerCorrect("CORRECT");
                        }

                        if (testTakerAnswers != null && !testTakerAnswers.isEmpty()) {
                            candidateQuestionResult.setAnswered(true);
                        }
                        candidateQuestionRepository.updateCandidateQuestion(candidateQuestionResult);

                    }
                });
            });
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }
    }

    /**
     * Updates the flagged status of a question.
     *
     * @param flaggedInputDTO The input data for flagging a question.
     * @throws ProcessFailedException If the process of flagging the question fails.
     */
    @Override
    public void updateFlaggedQuestion(FlaggedInputDTO flaggedInputDTO) {
        try {
            String assessmentTestPk = KeyBuilder.assessmentTestPK(flaggedInputDTO.getOrganizationId(), flaggedInputDTO.getAssessmentId());
            String assessmentTestSK = KeyBuilder.assessmentTestSK(flaggedInputDTO.getAssessmentId(), flaggedInputDTO.getTestId());

            String testQuestionPK = KeyBuilder.testQuestionPK(flaggedInputDTO.getOrganizationId(), flaggedInputDTO.getAssessmentId(), flaggedInputDTO.getTestId());
            String testQuestionSK = KeyBuilder.testQuestionSK(flaggedInputDTO.getQuestionId());

            String candidateQuestionPK = KeyBuilder.candidateQuestionPK(flaggedInputDTO.getOrganizationId(), flaggedInputDTO.getAssessmentId(), flaggedInputDTO.getTestId());
            String candidateQuestionSK = KeyBuilder.candidateQuestionSK(flaggedInputDTO.getQuestionId(), flaggedInputDTO.getTestTakerEmail(), flaggedInputDTO.getTestTakerId());


            Key assessmentKey = Key.builder().partitionValue(assessmentTestPk).sortValue(assessmentTestSK).build();
            Key testQuestionKey = Key.builder().partitionValue(testQuestionPK).sortValue(testQuestionSK).build();
            Key candidateQuestionKey = Key.builder().partitionValue(candidateQuestionPK).sortValue(candidateQuestionSK).build();

            TestQuestion testQuestion1 = testQuestionRepository.getTestQuestion(testQuestionKey);
            AssessmentTest assessmentTest1 = assessmentTestRepository.getAssessmentTest(assessmentKey);
            CandidateQuestionResult candidateQuestionResult1 = candidateQuestionRepository.getCandidateQuestion(candidateQuestionKey);

            testQuestion1.setNumberOfFlags(testQuestion1.getNumberOfFlags() + 1);
            assessmentTest1.setNumberOfFlags(assessmentTest1.getNumberOfFlags() + 1);
            candidateQuestionResult1.setIsFlagged("true");


            candidateQuestionRepository.updateCandidateQuestion(candidateQuestionResult1);
            assessmentTestRepository.updateAssessmentTest(assessmentTest1);
            testQuestionRepository.updateTestQuestion(testQuestion1);
        } catch (Exception exception) {
            log.error(exception.getMessage());
            throw new ProcessFailedException(exception.getMessage());
        }


    }

    /**
     * Updates the unflagged status of a question.
     *
     * @param flaggedInputDTO The input data for unflagging a question.
     * @throws ProcessFailedException If the process of unflagging the question fails.
     */
    @Override
    public void updateUnflaggedQuestion(FlaggedInputDTO flaggedInputDTO) {
        Key assessmentKey = null;
        Key testQuestionKey = null;
        Key candidateQuestionKey = null;
        try {
            String assessmentTestPk = KeyBuilder.assessmentTestPK(flaggedInputDTO.getOrganizationId(), flaggedInputDTO.getAssessmentId());
            String assessmentTestSK = KeyBuilder.assessmentTestSK(flaggedInputDTO.getAssessmentId(), flaggedInputDTO.getTestId());

            String testQuestionPK = KeyBuilder.testQuestionPK(flaggedInputDTO.getOrganizationId(), flaggedInputDTO.getAssessmentId(), flaggedInputDTO.getTestId());
            String testQuestionSK = KeyBuilder.testQuestionSK(flaggedInputDTO.getQuestionId());

            String candidateQuestionPK = KeyBuilder.candidateQuestionPK(flaggedInputDTO.getOrganizationId(), flaggedInputDTO.getAssessmentId(), flaggedInputDTO.getTestId());
            String candidateQuestionSK = KeyBuilder.candidateQuestionSK(flaggedInputDTO.getQuestionId(), flaggedInputDTO.getTestTakerEmail(), flaggedInputDTO.getTestTakerId());


            assessmentKey = Key.builder().partitionValue(assessmentTestPk).sortValue(assessmentTestSK).build();
            testQuestionKey = Key.builder().partitionValue(testQuestionPK).sortValue(testQuestionSK).build();
            candidateQuestionKey = Key.builder().partitionValue(candidateQuestionPK).sortValue(candidateQuestionSK).build();
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        TestQuestion testQuestion1;
        AssessmentTest assessmentTest1;
        CandidateQuestionResult candidateQuestionResult1;
        try {
            testQuestion1 = testQuestionRepository.getTestQuestion(testQuestionKey);
            assessmentTest1 = assessmentTestRepository.getAssessmentTest(assessmentKey);
            candidateQuestionResult1 = candidateQuestionRepository.getCandidateQuestion(candidateQuestionKey);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }


        assert testQuestion1 != null;
        int currentFlagsForTestQuestion1 = testQuestion1.getNumberOfFlags();
        if (currentFlagsForTestQuestion1 > 0) {
            testQuestion1.setNumberOfFlags(currentFlagsForTestQuestion1 - 1);
        }

        assert assessmentTest1 != null;
        int currentFlagsForAssessmentTest1 = assessmentTest1.getNumberOfFlags();
        if (currentFlagsForAssessmentTest1 > 0) {
            assessmentTest1.setNumberOfFlags(currentFlagsForAssessmentTest1 - 1);
        }
        assert candidateQuestionResult1 != null;
        candidateQuestionResult1.setIsFlagged("false");


        try {
            candidateQuestionRepository.updateCandidateQuestion(candidateQuestionResult1);
            assessmentTestRepository.updateAssessmentTest(assessmentTest1);
            testQuestionRepository.updateTestQuestion(testQuestion1);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ProcessFailedException(e.getMessage());
        }

    }


}
