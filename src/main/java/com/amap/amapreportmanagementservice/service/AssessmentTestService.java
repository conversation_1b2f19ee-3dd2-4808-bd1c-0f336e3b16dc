package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.assessmentDetails_response_page_dtos.TestInfoDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;

import java.util.List;

public interface AssessmentTestService {

    void saveAssessmentTest(AssessmentProgressDTO assessmentProgressDTO);

    void saveAssessmentTest(AssessmentInputDTO assessmentInputDTO);

    void updateAssessmentTest(AssessmentInputDTO assessmentInputDTO);

    List<TestInfoDTO> getAssessmentTestInfo(String organizationId, String assessmentId);

    void getAssessmentTestAverages(String organizationId, String assessmentId);


}