package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateAssessmentDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateTestDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import com.amap.amapreportmanagementservice.repository.CandidateAssessmentRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CandidateAssessmentImplModifiedMethodsTest {

    @Mock
    private CandidateAssessmentRepository candidateAssessmentRepository;

    @Mock
    private CandidateTestService candidateTestService;

    @Mock
    private CandidateQuestionService candidateQuestionService;

    @Mock
    private AssessmentService assessmentService;

    @Mock
    private AssessmentTestService assessmentTestService;

    @Mock
    private TestQuestionService testQuestionService;

    @Mock
    private WebhookReportJobService webhookReportJobService;

    @InjectMocks
    private CandidateAssessmentImpl candidateAssessmentService;

    private AssessmentProgressDTO assessmentProgressDTO;
    private AssessmentInputDTO assessmentInputDTO;
    private CandidateAssessment candidateAssessment;

    @BeforeEach
    void setUp() {
        // Setup AssessmentProgressDTO for in-progress assessments
        CandidateTestDTO candidateTestDTO = CandidateTestDTO.builder()
                .id("test-123")
                .title("Math Test")
                .build();

        CandidateAssessmentDTO candidateAssessmentDTO = CandidateAssessmentDTO.builder()
                .id("assessment-456")
                .title("Sample Assessment")
                .assessmentStartTime("2024-01-01T10:00:00")
                .assessmentDuration(3600)
                .tests(Arrays.asList(candidateTestDTO))
                .build();

        assessmentProgressDTO = AssessmentProgressDTO.builder()
                .id("candidate-789")
                .assessmentId("assessment-456")
                .organizationId("org-123")
                .email("<EMAIL>")
                .status("IN_PROGRESS")
                .assessment(candidateAssessmentDTO)
                .build();

        // Setup AssessmentInputDTO for completed assessments
        TestResultInputDTO testResultInputDTO = TestResultInputDTO.builder()
                .testId("test-123")
                .totalScore(100)
                .build();

        assessmentInputDTO = AssessmentInputDTO.builder()
                .organizationId("org-123")
                .assessmentId("assessment-456")
                .email("<EMAIL>")
                .testTakerId("candidate-789")
                .status("COMPLETED")
                .testResults(Arrays.asList(testResultInputDTO))
                .build();

        // Setup existing candidate assessment
        candidateAssessment = new CandidateAssessment();
        candidateAssessment.setPk("org-123#assessment-456");
        candidateAssessment.setSk("<EMAIL>#candidate-789");
        candidateAssessment.setAssessmentWindowViolationCount(0);
        candidateAssessment.setAssessmentTakerViolationShotCount(0);
    }

    @Test
    void testSaveCandidateAssessment_DoesNotSaveQuestions() {
        doNothing().when(candidateAssessmentRepository).saveCandidateAssessment(any(CandidateAssessment.class));
        doNothing().when(candidateTestService).saveCandidateTest(any(AssessmentProgressDTO.class));
        doNothing().when(assessmentService).saveAssessment(any(AssessmentProgressDTO.class));

        candidateAssessmentService.saveCandidateAssessment(assessmentProgressDTO);

        // Verify that candidate assessment is saved
        verify(candidateAssessmentRepository, times(1)).saveCandidateAssessment(any(CandidateAssessment.class));
        
        // Verify that other services are called based on actual implementation
        verify(candidateTestService, times(1)).saveCandidateTest(eq(assessmentProgressDTO));
        verify(assessmentService, times(1)).saveAssessment(eq(assessmentProgressDTO));
        
        // These services are NOT called in saveCandidateAssessment according to implementation
        verify(assessmentTestService, never()).saveAssessmentTest(any(AssessmentProgressDTO.class));
        verify(testQuestionService, never()).saveTestQuestions(any(AssessmentProgressDTO.class));
        verify(candidateQuestionService, never()).saveCandidateQuestionResult(any(AssessmentProgressDTO.class));
        verify(candidateQuestionService, never()).saveCandidateQuestionResultFromAssessmentInput(any(AssessmentInputDTO.class));
    }

    @Test
    void testSaveCandidateAssessment_VerifyEntityFields() {
        doNothing().when(candidateAssessmentRepository).saveCandidateAssessment(any(CandidateAssessment.class));
        doNothing().when(candidateTestService).saveCandidateTest(any(AssessmentProgressDTO.class));
        doNothing().when(assessmentService).saveAssessment(any(AssessmentProgressDTO.class));

        candidateAssessmentService.saveCandidateAssessment(assessmentProgressDTO);

        verify(candidateAssessmentRepository).saveCandidateAssessment(argThat(assessment -> 
            "candidate-789".equals(assessment.getCandidateId()) &&
            "<EMAIL>".equals(assessment.getCandidateEmail()) &&
            "assessment-456".equals(assessment.getAssessmentId()) &&
            "org-123".equals(assessment.getOrganizationId()) &&
            "IN_PROGRESS".equals(assessment.getStatus()) &&
            "Sample Assessment".equals(assessment.getTitle()) &&
            assessment.getAssessmentDuration() == 3600
        ));
    }

    @Test
    void testUpdateCandidateAssessment_SavesInsteadOfUpdates() {
        when(candidateAssessmentRepository.getSpecificCandidateAssessment(any(Key.class)))
                .thenReturn(candidateAssessment);
        doNothing().when(candidateAssessmentRepository).updateCandidateAssessment(any(CandidateAssessment.class));
        doNothing().when(candidateTestService).updateCandidateTest(any(AssessmentInputDTO.class));
        doNothing().when(candidateQuestionService).saveCandidateQuestionResultFromAssessmentInput(any(AssessmentInputDTO.class));
        doNothing().when(testQuestionService).saveTestQuestions(any(AssessmentInputDTO.class));
        doNothing().when(assessmentTestService).saveAssessmentTest(any(AssessmentInputDTO.class));
        doNothing().when(assessmentService).updateAssessment(any(AssessmentInputDTO.class));

        candidateAssessmentService.updateCandidateAssessment(assessmentInputDTO);

        // Verify that candidate assessment is updated (not saved)
        verify(candidateAssessmentRepository, times(1)).updateCandidateAssessment(any(CandidateAssessment.class));
        
        // Verify that candidate test is updated
        verify(candidateTestService, times(1)).updateCandidateTest(eq(assessmentInputDTO));
        
        // Verify that questions are SAVED (not updated) for completed assessments
        verify(candidateQuestionService, times(1)).saveCandidateQuestionResultFromAssessmentInput(eq(assessmentInputDTO));
        verify(candidateQuestionService, never()).updateCandidateQuestionResult(any(AssessmentInputDTO.class));
        
        // Verify that test questions are SAVED (not updated)
        verify(testQuestionService, times(1)).saveTestQuestions(eq(assessmentInputDTO));
        verify(testQuestionService, never()).updateTestQuestions(any(AssessmentInputDTO.class));
        
        // Verify that assessment tests are SAVED (not updated)
        verify(assessmentTestService, times(1)).saveAssessmentTest(eq(assessmentInputDTO));
        verify(assessmentTestService, never()).updateAssessmentTest(any(AssessmentInputDTO.class));
        
        // Verify that assessment is still updated
        verify(assessmentService, times(1)).updateAssessment(eq(assessmentInputDTO));
    }

    @Test
    void testUpdateCandidateAssessment_WithWebhookCallback() {
        candidateAssessment.setReportCallbackURL("https://example.com/webhook");
        
        when(candidateAssessmentRepository.getSpecificCandidateAssessment(any(Key.class)))
                .thenReturn(candidateAssessment);
        doNothing().when(candidateAssessmentRepository).updateCandidateAssessment(any(CandidateAssessment.class));
        doNothing().when(candidateTestService).updateCandidateTest(any(AssessmentInputDTO.class));
        doNothing().when(candidateQuestionService).saveCandidateQuestionResultFromAssessmentInput(any(AssessmentInputDTO.class));
        doNothing().when(testQuestionService).saveTestQuestions(any(AssessmentInputDTO.class));
        doNothing().when(assessmentTestService).saveAssessmentTest(any(AssessmentInputDTO.class));
        doNothing().when(assessmentService).updateAssessment(any(AssessmentInputDTO.class));
        doNothing().when(webhookReportJobService).saveWebhookReportJob(any(CandidateAssessment.class));

        candidateAssessmentService.updateCandidateAssessment(assessmentInputDTO);

        // Verify webhook is called when callback URL is present
        verify(webhookReportJobService, times(1)).saveWebhookReportJob(eq(candidateAssessment));
    }

    @Test
    void testUpdateCandidateAssessment_WithoutWebhookCallback() {
        candidateAssessment.setReportCallbackURL(null);
        
        when(candidateAssessmentRepository.getSpecificCandidateAssessment(any(Key.class)))
                .thenReturn(candidateAssessment);
        doNothing().when(candidateAssessmentRepository).updateCandidateAssessment(any(CandidateAssessment.class));
        doNothing().when(candidateTestService).updateCandidateTest(any(AssessmentInputDTO.class));
        doNothing().when(candidateQuestionService).saveCandidateQuestionResultFromAssessmentInput(any(AssessmentInputDTO.class));
        doNothing().when(testQuestionService).saveTestQuestions(any(AssessmentInputDTO.class));
        doNothing().when(assessmentTestService).saveAssessmentTest(any(AssessmentInputDTO.class));
        doNothing().when(assessmentService).updateAssessment(any(AssessmentInputDTO.class));

        candidateAssessmentService.updateCandidateAssessment(assessmentInputDTO);

        // Verify webhook is NOT called when callback URL is null
        verify(webhookReportJobService, never()).saveWebhookReportJob(any(CandidateAssessment.class));
    }

    @Test
    void testUpdateCandidateAssessment_CalculatesScoresCorrectly() {
        when(candidateAssessmentRepository.getSpecificCandidateAssessment(any(Key.class)))
                .thenReturn(candidateAssessment);
        doNothing().when(candidateAssessmentRepository).updateCandidateAssessment(any(CandidateAssessment.class));
        doNothing().when(candidateTestService).updateCandidateTest(any(AssessmentInputDTO.class));
        doNothing().when(candidateQuestionService).saveCandidateQuestionResultFromAssessmentInput(any(AssessmentInputDTO.class));
        doNothing().when(testQuestionService).saveTestQuestions(any(AssessmentInputDTO.class));
        doNothing().when(assessmentTestService).saveAssessmentTest(any(AssessmentInputDTO.class));
        doNothing().when(assessmentService).updateAssessment(any(AssessmentInputDTO.class));

        // Add test result with specific scores
        assessmentInputDTO.getTestResults().get(0).setTotalPassedScore(80.0);
        assessmentInputDTO.getTestResults().get(0).setTotalScore(100);

        candidateAssessmentService.updateCandidateAssessment(assessmentInputDTO);

        verify(candidateAssessmentRepository).updateCandidateAssessment(argThat(assessment -> 
            assessment.getCandidateMarks() == 80.0 &&
            assessment.getTotalScore() == 100.0 &&
            assessment.getScorePercentage() == 80.0 &&
            "COMPLETED".equals(assessment.getStatus())
        ));
    }
}
