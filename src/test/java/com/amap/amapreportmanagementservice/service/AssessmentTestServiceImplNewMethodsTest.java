package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.QuestionResultInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.AssessmentTest;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.AssessmentRepository;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.CandidateTestRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AssessmentTestServiceImplNewMethodsTest {

    @Mock
    private AssessmentTestRepository assessmentTestRepository;

    @Mock
    private CandidateTestRepository candidateTestRepository;

    @Mock
    private AssessmentRepository assessmentRepository;

    @InjectMocks
    private AssessmentTestServiceImpl assessmentTestService;

    private AssessmentInputDTO assessmentInputDTO;
    private TestResultInputDTO testResultInputDTO;
    private QuestionResultInputDTO questionResultInputDTO;

    @BeforeEach
    void setUp() {
        // Setup test data
        questionResultInputDTO = QuestionResultInputDTO.builder()
                .questionId("question-123")
                .build();

        testResultInputDTO = TestResultInputDTO.builder()
                .testId("test-456")
                .questionResults(Arrays.asList(questionResultInputDTO))
                .numberOfQuestions(5)
                .totalScore(100)
                .build();

        assessmentInputDTO = AssessmentInputDTO.builder()
                .organizationId("org-789")
                .assessmentId("assessment-101")
                .testTakerId("candidate-123")
                .testResults(Arrays.asList(testResultInputDTO))
                .build();
    }

    @Test
    void testSaveAssessmentTest_Success() {
        when(assessmentTestRepository.checkIfExists(anyString(), anyString(), anyString()))
                .thenReturn(false);
        doNothing().when(assessmentTestRepository).saveAssessmentTest(any(AssessmentTest.class));

        assessmentTestService.saveAssessmentTest(assessmentInputDTO);

        verify(assessmentTestRepository, times(1)).checkIfExists("org-789", "assessment-101", "test-456");
        verify(assessmentTestRepository, times(1)).saveAssessmentTest(any(AssessmentTest.class));
    }

    @Test
    void testSaveAssessmentTest_SkipExistingTest() {
        when(assessmentTestRepository.checkIfExists(anyString(), anyString(), anyString()))
                .thenReturn(true); // Test already exists

        assessmentTestService.saveAssessmentTest(assessmentInputDTO);

        verify(assessmentTestRepository, times(1)).checkIfExists("org-789", "assessment-101", "test-456");
        verify(assessmentTestRepository, never()).saveAssessmentTest(any(AssessmentTest.class));
    }

    @Test
    void testSaveAssessmentTest_MultipleTests() {
        // Setup second test
        QuestionResultInputDTO question2 = QuestionResultInputDTO.builder()
                .questionId("question-456")
                .build();

        TestResultInputDTO test2 = TestResultInputDTO.builder()
                .testId("test-789")
                .questionResults(Arrays.asList(question2))
                .numberOfQuestions(3)
                .totalScore(75)
                .build();

        List<TestResultInputDTO> testResults = new ArrayList<>(assessmentInputDTO.getTestResults());
        testResults.add(test2);
        assessmentInputDTO.setTestResults(testResults);

        when(assessmentTestRepository.checkIfExists(anyString(), anyString(), anyString()))
                .thenReturn(false);
        doNothing().when(assessmentTestRepository).saveAssessmentTest(any(AssessmentTest.class));

        assessmentTestService.saveAssessmentTest(assessmentInputDTO);

        // Should save 2 tests
        verify(assessmentTestRepository, times(2)).saveAssessmentTest(any(AssessmentTest.class));
        verify(assessmentTestRepository, times(2)).checkIfExists(anyString(), anyString(), anyString());
    }

    @Test
    void testSaveAssessmentTest_VerifyEntityFields() {
        when(assessmentTestRepository.checkIfExists(anyString(), anyString(), anyString()))
                .thenReturn(false);
        doNothing().when(assessmentTestRepository).saveAssessmentTest(any(AssessmentTest.class));

        assessmentTestService.saveAssessmentTest(assessmentInputDTO);

        verify(assessmentTestRepository).saveAssessmentTest(argThat(assessmentTest -> 
            "assessment-101".equals(assessmentTest.getAssessmentId()) &&
            "test-456".equals(assessmentTest.getTestId()) &&
            "org-789".equals(assessmentTest.getOrganizationId()) &&
            assessmentTest.getNumberOfQuestions() == 5 &&
            assessmentTest.getTotalScore() == 100 &&
            assessmentTest.getQuestionIds().contains("question-123") &&
            assessmentTest.getQuestionIds().size() == 1 &&
            assessmentTest.getCreatedAt() != null
        ));
    }

    @Test
    void testSaveAssessmentTest_WithMultipleQuestions() {
        // Add more questions to the test
        QuestionResultInputDTO question2 = QuestionResultInputDTO.builder()
                .questionId("question-456")
                .build();
        
        QuestionResultInputDTO question3 = QuestionResultInputDTO.builder()
                .questionId("question-789")
                .build();

        List<QuestionResultInputDTO> questionResults = new ArrayList<>(testResultInputDTO.getQuestionResults());
        questionResults.addAll(Arrays.asList(question2, question3));
        testResultInputDTO.setQuestionResults(questionResults);

        when(assessmentTestRepository.checkIfExists(anyString(), anyString(), anyString()))
                .thenReturn(false);
        doNothing().when(assessmentTestRepository).saveAssessmentTest(any(AssessmentTest.class));

        assessmentTestService.saveAssessmentTest(assessmentInputDTO);

        verify(assessmentTestRepository).saveAssessmentTest(argThat(assessmentTest -> 
            assessmentTest.getQuestionIds().size() == 3 &&
            assessmentTest.getQuestionIds().contains("question-123") &&
            assessmentTest.getQuestionIds().contains("question-456") &&
            assessmentTest.getQuestionIds().contains("question-789")
        ));
    }

    @Test
    void testSaveAssessmentTest_Exception() {
        when(assessmentTestRepository.checkIfExists(anyString(), anyString(), anyString()))
                .thenReturn(false);
        doThrow(new RuntimeException("Database error")).when(assessmentTestRepository)
                .saveAssessmentTest(any(AssessmentTest.class));

        assertThrows(ProcessFailedException.class, () -> 
            assessmentTestService.saveAssessmentTest(assessmentInputDTO)
        );
    }

    @Test
    void testSaveAssessmentTest_MixedExistingAndNewTests() {
        // Setup second test
        TestResultInputDTO test2 = TestResultInputDTO.builder()
                .testId("test-789")
                .questionResults(Arrays.asList(questionResultInputDTO))
                .build();

        List<TestResultInputDTO> testResults = new ArrayList<>(assessmentInputDTO.getTestResults());
        testResults.add(test2);
        assessmentInputDTO.setTestResults(testResults);

        // First test exists, second doesn't
        when(assessmentTestRepository.checkIfExists("org-789", "assessment-101", "test-456"))
                .thenReturn(true);
        when(assessmentTestRepository.checkIfExists("org-789", "assessment-101", "test-789"))
                .thenReturn(false);
        doNothing().when(assessmentTestRepository).saveAssessmentTest(any(AssessmentTest.class));

        assessmentTestService.saveAssessmentTest(assessmentInputDTO);

        // Should only save the second test
        verify(assessmentTestRepository, times(1)).saveAssessmentTest(any(AssessmentTest.class));
        verify(assessmentTestRepository, times(2)).checkIfExists(anyString(), anyString(), anyString());
    }

    @Test
    void testSaveAssessmentTest_EmptyTestResults() {
        assessmentInputDTO.setTestResults(Arrays.asList());

        assessmentTestService.saveAssessmentTest(assessmentInputDTO);

        verify(assessmentTestRepository, never()).checkIfExists(anyString(), anyString(), anyString());
        verify(assessmentTestRepository, never()).saveAssessmentTest(any(AssessmentTest.class));
    }

    @Test
    void testSaveAssessmentTest_EmptyQuestionResults() {
        testResultInputDTO.setQuestionResults(Arrays.asList());

        assessmentTestService.saveAssessmentTest(assessmentInputDTO);

        verify(assessmentTestRepository).saveAssessmentTest(argThat(assessmentTest -> 
            assessmentTest.getQuestionIds().isEmpty()
        ));
    }
}
