package com.amap.amapreportmanagementservice.service;

import com.amap.amapreportmanagementservice.dto.AssessmentScoreMetricsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateScoreMetricsDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.CandidateTrajectoryDTO;
import com.amap.amapreportmanagementservice.dto.candidate_score_metrics_dtos.ComparativeAnalysisDTO;
import com.amap.amapreportmanagementservice.dto.progress.AssessmentProgressDTO;
import com.amap.amapreportmanagementservice.dto.progress.CandidateAssessmentDTO;
import com.amap.amapreportmanagementservice.dto.results.AssessmentInputDTO;
import com.amap.amapreportmanagementservice.dto.results.TestResultInputDTO;
import com.amap.amapreportmanagementservice.entity.Assessment;
import com.amap.amapreportmanagementservice.entity.CandidateAssessment;
import com.amap.amapreportmanagementservice.exceptions.CandidateNotFoundException;
import com.amap.amapreportmanagementservice.exceptions.ProcessFailedException;
import com.amap.amapreportmanagementservice.repository.AssessmentRepository;
import com.amap.amapreportmanagementservice.repository.AssessmentTestRepository;
import com.amap.amapreportmanagementservice.repository.CandidateAssessmentRepository;
import com.amap.amapreportmanagementservice.repository.CandidateTestRepository;
import com.amap.amapreportmanagementservice.utils.KeyBuilder;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;
import software.amazon.awssdk.enhanced.dynamodb.Key;

import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.amap.amapreportmanagementservice.TestConstants.*;
import static com.amap.amapreportmanagementservice.constants.DbKeysPrefixes.CANDIDATE_EMAIL_PREFIX;
import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.assertj.core.api.Fail.fail;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.*;

class CandidateAssessmentImplTest extends BaseTestClass {
    @Mock
    CandidateAssessmentRepository candidateAssessmentRepository;
    @Mock
    AssessmentRepository assessmentRepository;
    @InjectMocks
    CandidateAssessmentImpl candidateAssessmentService;

    @Mock
    private AssessmentService assessmentService;
    @Mock
    private CandidateTestService candidateTestService;
    @Mock
    private TestQuestionService testQuestionService;
    @Mock
    private AssessmentTestService assessmentTestService;
    @Mock
    private CandidateQuestionService candidateQuestionService;
    @Mock
    private  CandidateTestRepository candidateTestRepository;
    @Mock
    private  AssessmentTestRepository assessmentTestRepository;

    @BeforeEach
    void setup() {
        MockitoAnnotations.openMocks(this);

        // Explicitly set all the dependencies
        ReflectionTestUtils.setField(candidateAssessmentService, "candidateTestService", candidateTestService);
        ReflectionTestUtils.setField(candidateAssessmentService, "candidateQuestionService", candidateQuestionService);
        ReflectionTestUtils.setField(candidateAssessmentService, "assessmentService", assessmentService);
        ReflectionTestUtils.setField(candidateAssessmentService, "assessmentTestService", assessmentTestService);
        ReflectionTestUtils.setField(candidateAssessmentService, "testQuestionService", testQuestionService);
        ReflectionTestUtils.setField(candidateAssessmentService, "candidateAssessmentRepository", candidateAssessmentRepository);

        // Set the additional dependencies shown in the service implementation
        ReflectionTestUtils.setField(candidateAssessmentService, "webhookReportJobService", mock(WebhookReportJobService.class));
        ReflectionTestUtils.setField(candidateAssessmentService, "assessmentRepository", assessmentRepository);
        ReflectionTestUtils.setField(candidateAssessmentService, "assessmentTestRepository", assessmentTestRepository);
        ReflectionTestUtils.setField(candidateAssessmentService, "candidateTestRepository", candidateTestRepository);
    }

    @Test
    void checkDependenciesAreInjected() {
        assertThat(candidateAssessmentService).isNotNull();
        assertThat(ReflectionTestUtils.getField(candidateAssessmentService, "assessmentService")).isNotNull();
        // Check other dependencies
    }

    @Test
    void updateCandidateAssessment_ShouldThrowException_WhenCandidateNotFound() {
        // Given
        AssessmentInputDTO assessmentInputDTO = new AssessmentInputDTO();
        Key candidateKey = Key.builder()
                .partitionValue("org#1")
                .sortValue("assess#1")
                .build();
        when(candidateAssessmentRepository.getSpecificCandidateAssessment(candidateKey)).thenReturn(null);

        // When & Then
        assertThatThrownBy(() -> candidateAssessmentService.updateCandidateAssessment(assessmentInputDTO))
                .isInstanceOf(CandidateNotFoundException.class)
                .hasMessage("Candidate Assessment does not exist");
    }


    @Test
    void saveCandidateAssessment() {
        // Arrange
        AssessmentProgressDTO assessmentDTO = createSampleAssessmentProgressDTO();

        // Act
        candidateAssessmentService.saveCandidateAssessment(assessmentDTO);

        // Assert - match the order in the implementation
        // First verify the repository call
        verify(candidateAssessmentRepository).saveCandidateAssessment(any(CandidateAssessment.class));

        // Then verify service calls in the order they appear in the implementation
        verify(candidateTestService).saveCandidateTest(assessmentDTO);
        verify(assessmentService).saveAssessment(assessmentDTO);
        
        // These services are NOT called in saveCandidateAssessment according to implementation
        verify(candidateQuestionService, never()).saveCandidateQuestionResult(assessmentDTO);
        verify(assessmentTestService, never()).saveAssessmentTest(assessmentDTO);
        verify(testQuestionService, never()).saveTestQuestions(assessmentDTO);
    }

    @Test
    void saveCandidateAssessment_WithNullInput_ShouldThrowException() {
        // Act & Assert
        Assertions.assertThrows(ProcessFailedException.class,
                () -> candidateAssessmentService.saveCandidateAssessment(null),
                "Expected saveCandidateAssessment to throw IllegalArgumentException for null input"
        );
    }



    @Test
    void throwExceptionWhenCandidateAssessmentDoesNotExist() {
        //Given
        String pk = KeyBuilder.candidateAssessmentPK(ORGANIZATIONID, ASSESSMENTID);
        String sk = KeyBuilder.candidateAssessmentSK(TESTEMAIL, CANDIDATEID);

        Key candidateKey = Key.builder().partitionValue(pk).sortValue(sk).build();

        given(candidateAssessmentRepository.getSpecificCandidateAssessment(candidateKey))
                .willReturn(null);

        AssessmentInputDTO assessmentInputDTO = new AssessmentInputDTO();
        assertThatThrownBy(() -> candidateAssessmentService.updateCandidateAssessment(assessmentInputDTO))
                .isInstanceOf(CandidateNotFoundException.class)
                .hasMessage("Candidate Assessment does not exist");

        verify(candidateAssessmentRepository, never()).updateCandidateAssessment(any());
    }

    @Test
    void testGetPercentageScoreDistributionWithNoCompletedAssessments() {

        List<CandidateAssessment> candidateAssessments = new ArrayList<>();
        // Add some CandidateAssessment objects to the list with statuses other than "Completed"
        candidateAssessments.add(new CandidateAssessment());
        candidateAssessments.add(new CandidateAssessment());

        when(candidateAssessmentRepository.getCandidateAssessment(any())).thenReturn(candidateAssessments);

        // Act
        List<Double> result = candidateAssessmentService.getPercentageScoreDistribution(ORGANIZATIONID, ASSESSMENTID);
        // Assert
        // Verify that the result is an empty list when there are no completed assessments
        assertTrue(result.isEmpty());
    }


    @Test
    void testAverageScoreMetricsWithCompletedAssessments() {
        // Mock assessment with a non-zero total score
        Assessment assessment = new Assessment();
        assessment.setTotalScore(100);
        when(assessmentRepository.getAssessment(any())).thenReturn(assessment);

        CandidateAssessment candidateAssessment1 = new CandidateAssessment();
        candidateAssessment1.setStatus(TESTCOMPLETEDSTATUS);
        candidateAssessment1.setCandidateMarks(90);

        CandidateAssessment candidateAssessment2 = new CandidateAssessment();
        candidateAssessment2.setStatus(TESTCOMPLETEDSTATUS);
        candidateAssessment2.setCandidateMarks(80);

        // Mock completed assessments with non-zero marks
        List<CandidateAssessment> candidateAssessments = new ArrayList<>();
        candidateAssessments.add(candidateAssessment1);
        candidateAssessments.add(candidateAssessment2);


        when(candidateAssessmentRepository.getCandidateAssessment(any())).thenReturn(candidateAssessments);

        // Act
        AssessmentScoreMetricsDTO result = candidateAssessmentService.averageScoreMetrics(ORGANIZATIONID, ASSESSMENTID);

        // Assert
        assertEquals(90.0, result.getHighestScore(), 0.001);
        assertEquals(80.0, result.getLowestScore(), 0.001);
    }

    @Test
    void testAverageScoreMetricsWithZeroTotalScore() {

        // Mock assessment with a zero total score
        Assessment assessment = new Assessment();
        assessment.setTotalScore(0);
        when(assessmentRepository.getAssessment(any())).thenReturn(assessment);

        // Mock completed assessments with non-zero marks
        CandidateAssessment candidateAssessment1 = new CandidateAssessment();
        candidateAssessment1.setStatus(TESTCOMPLETEDSTATUS);
        candidateAssessment1.setTotalScore(80);

        CandidateAssessment candidateAssessment2 = new CandidateAssessment();
        candidateAssessment2.setStatus(TESTCOMPLETEDSTATUS);
        candidateAssessment2.setTotalScore(90);
        // Mock completed assessments with non-zero marks
        List<CandidateAssessment> candidateAssessments = new ArrayList<>();
        candidateAssessments.add(candidateAssessment1);
        candidateAssessments.add(candidateAssessment2);
        when(candidateAssessmentRepository.getCandidateAssessment(any())).thenReturn(candidateAssessments);

        // Act
        AssessmentScoreMetricsDTO result = candidateAssessmentService.averageScoreMetrics(ORGANIZATIONID, ASSESSMENTID);

        // Assert
        assertEquals(0.0, result.getAverageScore(), 0.001);
        assertEquals(0.0, result.getHighestScore(), 0.001);
        assertEquals(0.0, result.getLowestScore(), 0.001);
    }

    @Test
    void testAverageScoreMetricsWithNoCompletedAssessments() {
        // Mock assessment with a non-zero total score
        Assessment assessment = new Assessment();
        assessment.setTotalScore(100);
        when(assessmentRepository.getAssessment(any())).thenReturn(assessment);

        // Mock assessments with a status other than "Completed"
        CandidateAssessment candidateAssessment1 = new CandidateAssessment();
        candidateAssessment1.setStatus("InProgress"); // Not completed
        candidateAssessment1.setTotalScore(80);

        CandidateAssessment candidateAssessment2 = new CandidateAssessment();
        candidateAssessment2.setStatus("NotStarted"); // Not completed
        candidateAssessment2.setTotalScore(90);

        List<CandidateAssessment> candidateAssessments = new ArrayList<>();
        candidateAssessments.add(candidateAssessment1);
        candidateAssessments.add(candidateAssessment2);
        when(candidateAssessmentRepository.getCandidateAssessment(any())).thenReturn(candidateAssessments);

        // Act
        AssessmentScoreMetricsDTO result = candidateAssessmentService.averageScoreMetrics(ORGANIZATIONID, ASSESSMENTID);

        // Assert
        assertEquals(0.0, result.getAverageScore(), 0.001);
        assertEquals(0.0, result.getHighestScore(), 0.001);
        assertEquals(0.0, result.getLowestScore(), 0.001);
    }

    @Test
    void testAverageScoreMetricsWithEmptyCompletedAssessments() {

        // Mock assessment with a non-zero total score
        Assessment assessment = new Assessment();
        assessment.setTotalScore(100);
        when(assessmentRepository.getAssessment(any())).thenReturn(assessment);

        // Mock an empty list of completed assessments
        List<CandidateAssessment> candidateAssessments = Collections.emptyList();
        when(candidateAssessmentRepository.getCandidateAssessment(any())).thenReturn(candidateAssessments);

        // Act
        AssessmentScoreMetricsDTO result = candidateAssessmentService.averageScoreMetrics(ORGANIZATIONID, ASSESSMENTID);

        // Assert
        assertEquals(0.0, result.getAverageScore(), 0.001);
        assertEquals(0.0, result.getHighestScore(), 0.001);
        assertEquals(0.0, result.getLowestScore(), 0.001);
    }


    @Test
    void getPercentageScoreDistribution() {
        // Create mock CandidateAssessment objects
        CandidateAssessment assessment1 = new CandidateAssessment();
        assessment1.setStatus(TESTCOMPLETEDSTATUS);
        assessment1.setScorePercentage(90.123);

        CandidateAssessment assessment2 = new CandidateAssessment();
        assessment2.setStatus(TESTCOMPLETEDSTATUS);
        assessment2.setScorePercentage(85.678);

        CandidateAssessment assessment3 = new CandidateAssessment();
        assessment3.setStatus(TESTINPROGRESSTATUS); // Should be filtered out
        assessment3.setScorePercentage(75.0);

        // Mock the behavior of the repository
        String pk = KeyBuilder.assessmentPK(ORGANIZATIONID, ASSESSMENTID);
        Key key = Key.builder().partitionValue(pk).sortValue(CANDIDATE_EMAIL_PREFIX).build();
        List<CandidateAssessment> mockAssessmentList = Arrays.asList(assessment1, assessment2, assessment3);
        when(candidateAssessmentRepository.getCandidateAssessment(key)).thenReturn(mockAssessmentList);

        // Call the method being tested
        List<Double> result = candidateAssessmentService.getPercentageScoreDistribution(ORGANIZATIONID, ASSESSMENTID);

        // Assertions
        assertEquals(2, result.size());
        assertEquals(90.12, result.get(0), 0.01);
        assertEquals(85.68, result.get(1), 0.01);

    }

    @Test
    void getCandidateScoreMetrics() {
        int integrityScore = 90;
        double scorePercentage = 95.5;
        String proctorLevel = "Level 1";

        // Create a mock CandidateAssessment object
        CandidateAssessment mockAssessment = new CandidateAssessment();
        mockAssessment.setCandidateId(CANDIDATEID);
        mockAssessment.setCandidateEmail(TESTEMAIL);
        mockAssessment.setCandidateMarks(100);
        mockAssessment.setIntegrityScore(integrityScore);
        mockAssessment.setScorePercentage(scorePercentage);
        mockAssessment.setProctorLevel(proctorLevel);

        // Mock the behavior of the repository
        String pk = KeyBuilder.candidateAssessmentPK(ORGANIZATIONID, ASSESSMENTID);
        Key key = Key.builder().partitionValue(pk).sortValue(CANDIDATE_EMAIL_PREFIX).build();
        List<CandidateAssessment> mockAssessmentList = new ArrayList<>();
        mockAssessmentList.add(mockAssessment);
        when(candidateAssessmentRepository.getCandidateAssessment(key)).thenReturn(mockAssessmentList);

        // Call the method being tested
        List<CandidateScoreMetricsDTO> result = candidateAssessmentService.getCandidateScoreMetrics(ORGANIZATIONID, ASSESSMENTID);

        // Assertions
        assertEquals(1, result.size());

        CandidateScoreMetricsDTO resultMetrics = result.get(0);
        assertEquals(CANDIDATEID, resultMetrics.getCandidateId());
        assertEquals(TESTEMAIL, resultMetrics.getCandidateEmail());
        assertEquals(100, resultMetrics.getCandidateMarks());
        assertEquals("Low Risk", resultMetrics.getRiskLevel()); // Assuming "90" maps to "Low Risk"
        assertEquals(integrityScore, resultMetrics.getIntegrityScore());
        assertEquals(95.5, resultMetrics.getScorePercentage());
        assertEquals(proctorLevel, resultMetrics.getProctoringLevel());

    }

//    @Test
//    void getComparedInfo() {
//        // Given
//        // Create the specific key that will be used in the service method
//        String candidatePK = KeyBuilder.candidateAssessmentPK(ORGANIZATIONID, ASSESSMENTID);
//        String candidateSK = KeyBuilder.candidateAssessmentSK(TESTEMAIL, CANDIDATEID);
//        Key candidateKey = Key.builder().partitionValue(candidatePK).sortValue(candidateSK).build();
//
//        CandidateAssessment mockCandidate = new CandidateAssessment();
//        mockCandidate.setCandidateMarks(90);
//
//        Assessment assessment1 = new Assessment();
//        assessment1.setTestsIds(Arrays.asList("test1", "test2"));
//        List<Assessment> mockAssessments = List.of(assessment1);
//
//        AssessmentTest assessmentTest1 = new AssessmentTest();
//        assessmentTest1.setTitle("Test 1");
//        assessmentTest1.setAverageScore(80.0);
//        assessmentTest1.setTotalScore(100);
//
//        AssessmentTest assessmentTest2 = new AssessmentTest();
//        assessmentTest2.setTitle("Test 2");
//        assessmentTest2.setAverageScore(75.0);
//        assessmentTest2.setTotalScore(100);
//
//        List<AssessmentTest> mockAssessmentTests = List.of(assessmentTest1, assessmentTest2);
//
//        CandidateTest mockCandidateTest1 = new CandidateTest();
//        mockCandidateTest1.setCandidateMarks(85);
//        mockCandidateTest1.setScorePercentage(85);
//
//        CandidateTest mockCandidateTest2 = new CandidateTest();
//        mockCandidateTest2.setCandidateMarks(80);
//        mockCandidateTest2.setScorePercentage(80);
//
//        // Use the exact key that will be created in the service method
//        when(candidateAssessmentRepository.getSpecificCandidateAssessment(candidateKey))
//                .thenReturn(mockCandidate);
//        when(assessmentRepository.getAssessments(any()))
//                .thenReturn(mockAssessments);
//        when(assessmentTestRepository.getAssessmentTestsWithoutCandidateTest(any()))
//                .thenReturn(mockAssessmentTests);
//        when(candidateTestRepository.getSpecificCandidateTest(any()))
//                .thenReturn(mockCandidateTest1, mockCandidateTest2);
//
//        // Act
//        List<ComparativeAnalysisDTO> result = candidateAssessmentService.getComparedInfo(
//                ORGANIZATIONID, ASSESSMENTID, TESTEMAIL, CANDIDATEID);
//
//        // Assert
//        assertEquals(2, result.size());
//        ComparativeAnalysisDTO firstResult = result.get(0);
//        assertEquals("Test 1", firstResult.getTestName());
//        assertEquals(80.0, firstResult.getTestAverageScore(), 0.01);
//        assertEquals(85.0, firstResult.getCandidateScore(), 0.01);
//
//        ComparativeAnalysisDTO secondResult = result.get(1);
//        assertEquals("Test 2", secondResult.getTestName());
//        assertEquals(75.0, secondResult.getTestAverageScore(), 0.01);
//        assertEquals(80.0, secondResult.getCandidateScore(), 0.01);
//    }
//
    @Test
    void getComparedInfo() {
        // Given
        CandidateAssessment mockCandidate = new CandidateAssessment();
        mockCandidate.setCandidateMarks(90);

        // Configure the mock with any() to ensure it returns data
        when(candidateAssessmentRepository.getSpecificCandidateAssessment(any()))
                .thenReturn(mockCandidate);

        // Other mocks as before...

        // Act
        List<ComparativeAnalysisDTO> result = candidateAssessmentService.getComparedInfo(
                ORGANIZATIONID, ASSESSMENTID, TESTEMAIL, CANDIDATEID);

        // Capture the actual key used
        ArgumentCaptor<Key> keyCaptor = ArgumentCaptor.forClass(Key.class);
        verify(candidateAssessmentRepository).getSpecificCandidateAssessment(keyCaptor.capture());
        Key actualKey = keyCaptor.getValue();

        // Print the actual key for debugging
        System.out.println("Actual key used: PK=" + actualKey.partitionKeyValue() + ", SK=" + actualKey.sortKeyValue());

        // Assertions as before
    }

    @Test
    void getComparedInfoThrowException(){
        //Given
        String candidatePK = KeyBuilder.candidateAssessmentPK(ORGANIZATIONID, ASSESSMENTID);
        String candidateSK = KeyBuilder.candidateAssessmentSK(TESTEMAIL, CANDIDATEID);
        Key candidateKey = Key.builder().partitionValue(candidatePK).sortValue(candidateSK).build();

        when(candidateAssessmentRepository.getSpecificCandidateAssessment(candidateKey)).thenReturn(null);

        assertThatThrownBy(() -> candidateAssessmentService.getComparedInfo(ORGANIZATIONID,ASSESSMENTID,TESTEMAIL,CANDIDATEID))
                .isInstanceOf(ProcessFailedException.class)
                .hasMessage("Candidate Assessment does not exist");

    }

    @Test
    void getCandidateTrajectory() {

        // Mock candidate PK and SK values
        String candidatePK = KeyBuilder.candidateAssessmentPK(ORGANIZATIONID, ASSESSMENTID);
        String candidateSK = KeyBuilder.candidateAssessmentSK(TESTEMAIL, CANDIDATEID);

        // Mock keys for candidate and trajectory
        Key candidateKey = Key.builder().partitionValue(candidatePK).sortValue(candidateSK).build();
        String canEmailSK = KeyBuilder.canEmailSk(TESTEMAIL);
        Key candidateTrajectoryKey = Key.builder().partitionValue(candidatePK).sortValue(canEmailSK).build();

        CandidateAssessment mockCandidate = new CandidateAssessment();
        mockCandidate.setTitle("Test Assessment");
        mockCandidate.setAssessmentStartTime("2023-09-20T10:00:00Z");
        mockCandidate.setCandidateMarks(90);

        List<CandidateAssessment> mockAssessments = new ArrayList<>();
        mockAssessments.add(mockCandidate);

        when(candidateAssessmentRepository.getSpecificCandidateAssessment(candidateKey))
                .thenReturn(mockCandidate);
        when(candidateAssessmentRepository.getCandidateAssessment(candidateTrajectoryKey))
                .thenReturn(mockAssessments);

        List<CandidateTrajectoryDTO> trajectoryList = candidateAssessmentService.getCandidateTrajectory(
                ORGANIZATIONID, ASSESSMENTID, TESTEMAIL, CANDIDATEID);

        // Assertions
        assertEquals(1, trajectoryList.size());
        CandidateTrajectoryDTO resultTrajectory = trajectoryList.get(0);
        assertEquals("Test Assessment", resultTrajectory.getAssessmentTitle());
        assertEquals("2023-09-20T10:00:00Z", resultTrajectory.getAssessmentStartTime());
        assertEquals(90, resultTrajectory.getAssessmentScore());

        // Verify that the repository methods were called with the expected keys
        verify(candidateAssessmentRepository).getSpecificCandidateAssessment(candidateKey);
        verify(candidateAssessmentRepository).getCandidateAssessment(candidateTrajectoryKey);

    }

    @Test
    void getCandidateTrajectoryThrowException(){
        assertThatThrownBy(() -> candidateAssessmentService.getCandidateTrajectory(ORGANIZATIONID,ASSESSMENTID,TESTEMAIL,CANDIDATEID))
                .isInstanceOf(ProcessFailedException.class)
                .hasMessage("Candidate Assessment does not exist");

    }


    // Helper method to create a sample AssessmentInputDTO for testing

    private AssessmentInputDTO createSampleAssessmentInputDTO() {
        AssessmentInputDTO inputDTO = new AssessmentInputDTO();
        inputDTO.setOrganizationId(ORGANIZATIONID);
        inputDTO.setAssessmentId(ASSESSMENTID);
        inputDTO.setStatus(TESTCOMPLETEDSTATUS);
        inputDTO.setEmail(TESTEMAIL);
        inputDTO.setTestTakerId(CANDIDATEID);
        inputDTO.setWindowViolation(Arrays.asList("violation1", "violation2"));
        inputDTO.setAssessmentEndTime(String.valueOf(ZonedDateTime.now()));
        inputDTO.setTestResults(createSampleTestResults());
        inputDTO.setReportCallbackURL("");
        return inputDTO;
    }

    // Helper method to create sample TestResultInputDTO objects for testing
    private List<TestResultInputDTO> createSampleTestResults() {
        TestResultInputDTO result1 = new TestResultInputDTO();
        result1.setTotalPassedScore(80);
        result1.setTotalScore(100);

        TestResultInputDTO result2 = new TestResultInputDTO();
        result2.setTotalPassedScore(90);
        result2.setTotalScore(100);

        return Arrays.asList(result1, result2);
    }

    // Helper method to create a sample CandidateAssessment for mocking
    private CandidateAssessment createSampleCandidateAssessment() {
        CandidateAssessment candidateAssessment = new CandidateAssessment();
        candidateAssessment.setStatus(TESTINPROGRESSTATUS);
        candidateAssessment.setCandidateMarks(0);
        candidateAssessment.setIntegrityScore(100);
        candidateAssessment.setScorePercentage(0.0);
        candidateAssessment.setAssessmentStartTime(ZonedDateTime.now().toString());
        candidateAssessment.setReportCallbackURL("");
        return candidateAssessment;
    }


    // Create a sample AssessmentProgressDTO for testing
    private AssessmentProgressDTO createSampleAssessmentProgressDTO() {
        // Create and return a sample AssessmentProgressDTO with the required fields set
        // Adjust the values as needed for your test case
        CandidateAssessmentDTO candidateAssessment = new CandidateAssessmentDTO();

        AssessmentProgressDTO assessmentDTO = new AssessmentProgressDTO();
        assessmentDTO.setOrganizationId(ORGANIZATIONID);
        assessmentDTO.setAssessmentId(ASSESSMENTID);
        assessmentDTO.setEmail(TESTEMAIL);
        assessmentDTO.setId(CANDIDATEID);
        assessmentDTO.setAssessment(candidateAssessment);
        assessmentDTO.setStatus(TESTCOMPLETEDSTATUS);

        // Set other necessary fields here
        return assessmentDTO;
    }


}

