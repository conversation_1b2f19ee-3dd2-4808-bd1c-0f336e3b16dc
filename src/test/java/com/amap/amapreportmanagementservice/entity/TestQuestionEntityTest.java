package com.amap.amapreportmanagementservice.entity;

import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class TestQuestionEntityTest { // Renamed to TestQuestionEntityTest

    @Test
    void testQuestion_BuilderAndGetterSetter_ShouldWork() {
        List<String> questionIds = Arrays.asList("q1", "q2", "q3");

        TestQuestion testQuestion = TestQuestion.builder()
                .assessmentId("assess123")
                .testId("test456")
                .questionId("question789")
                .questionText("What is the capital?")
                .domain("Geography")
                .category("Capitals")
                .numberOfTimesMissed(5)
                .numberOfTimesAnswered(20)
                .title("Capital Question")
                .duration(60)
                .difficultyLevel("Medium")
                .domainId("domain1")
                .categoryId("category1")
                .numberOfFlags(2)
                .questionIds(questionIds)
                .totalScore(10.0)
                .build();

        assertEquals("assess123", testQuestion.getAssessmentId());
        assertEquals("test456", testQuestion.getTestId());
        assertEquals("question789", testQuestion.getQuestionId());
        assertEquals("What is the capital?", testQuestion.getQuestionText());
        assertEquals("Geography", testQuestion.getDomain());
        assertEquals("Capitals", testQuestion.getCategory());
        assertEquals(5, testQuestion.getNumberOfTimesMissed());
        assertEquals(20, testQuestion.getNumberOfTimesAnswered());
        assertEquals("Capital Question", testQuestion.getTitle());
        assertEquals(60, testQuestion.getDuration());
        assertEquals("Medium", testQuestion.getDifficultyLevel());
        assertEquals("domain1", testQuestion.getDomainId());
        assertEquals("category1", testQuestion.getCategoryId());
        assertEquals(2, testQuestion.getNumberOfFlags());
        assertEquals(questionIds, testQuestion.getQuestionIds());
        assertEquals(10.0, testQuestion.getTotalScore());

        testQuestion.setQuestionId("newQuestion789");
        assertEquals("newQuestion789", testQuestion.getQuestionId());
    }

    @Test
    void testQuestion_NoArgsConstructor_ShouldCreateObject() {
        TestQuestion testQuestion = new TestQuestion();
        assertNotNull(testQuestion);
    }

    @Test
    void testQuestion_AllArgsConstructor_ShouldCreateObject() {
        // Setup test data
        List<String> questionIds = Arrays.asList("q1", "q2", "q3");
        String codeTemplates = "template1";
        String codeConstraints = "constraint1";

        // Test using builder pattern since constructor testing is complex with inheritance
        TestQuestion testQuestion = TestQuestion.builder()
                .assessmentId("assess123")
                .testId("test456")
                .questionId("question789")
                .questionText("What is the capital?")
                .domain("Geography")
                .category("Capitals")
                .numberOfTimesMissed(5)
                .numberOfTimesAnswered(20)
                .title("Capital Question")
                .duration(60)
                .difficultyLevel("Medium")
                .domainId("domain1")
                .categoryId("category1")
                .numberOfFlags(2)
                .questionIds(questionIds)
                .totalScore(10.0)
                .codeTemplate(codeTemplates)
                .codeConstraint(codeConstraints)
                .build();

        // Set inherited fields manually
        testQuestion.setPk("pk1");
        testQuestion.setSk("sk1");
        testQuestion.setOrganizationId("org123");
        testQuestion.setCreatedAt("2023-01-01");
        testQuestion.setUpdatedAt("2023-01-02");

        // Assertions
        assertEquals("assess123", testQuestion.getAssessmentId());
        assertEquals("test456", testQuestion.getTestId());
        assertEquals("question789", testQuestion.getQuestionId());
        assertEquals("What is the capital?", testQuestion.getQuestionText());
        assertEquals("Geography", testQuestion.getDomain());
        assertEquals("Capitals", testQuestion.getCategory());
        assertEquals(5, testQuestion.getNumberOfTimesMissed());
        assertEquals(20, testQuestion.getNumberOfTimesAnswered());
        assertEquals("Capital Question", testQuestion.getTitle());
        assertEquals(60, testQuestion.getDuration());
        assertEquals("Medium", testQuestion.getDifficultyLevel());
        assertEquals("domain1", testQuestion.getDomainId());
        assertEquals("category1", testQuestion.getCategoryId());
        assertEquals(2, testQuestion.getNumberOfFlags());
        assertEquals(questionIds, testQuestion.getQuestionIds());
        assertEquals(10.0, testQuestion.getTotalScore());
        assertEquals(codeTemplates, testQuestion.getCodeTemplate());
        assertEquals(codeConstraints, testQuestion.getCodeConstraint());
    }

    @Test
    void testQuestion_EqualsAndHashCode_ShouldWork() {
        TestQuestion question1 = TestQuestion.builder()
                .assessmentId("assess123")
                .testId("test456")
                .questionId("question789")
                .build();

        TestQuestion question2 = TestQuestion.builder()
                .assessmentId("assess123")
                .testId("test456")
                .questionId("question789")
                .build();

        TestQuestion question3 = TestQuestion.builder()
                .assessmentId("assess456")
                .testId("test456")
                .questionId("question789")
                .build();

        assertEquals(question1, question2);
        assertEquals(question1.hashCode(), question2.hashCode());
        assertNotEquals(question1, question3);
        assertNotEquals(question1.hashCode(), question3.hashCode());
    }

    @Test
    void testQuestion_ToString_ShouldNotReturnNull() {
        TestQuestion testQuestion = TestQuestion.builder()
                .assessmentId("assess123")
                .testId("test456")
                .questionId("question789")
                .build();
        assertNotNull(testQuestion.toString());
    }
}